#!/usr/bin/env python3
"""
Local Software Activation System - End User Application
Main application with local license verification
"""

import tkinter as tk
from tkinter import messagebox
import hashlib
import json
import os
import base64
import uuid
import platform
import subprocess
import webbrowser

# Configuration
LICENSE_FILE = "license.key"
SECRET_KEY = "SecureKey2024!@#$%"  # Must match license generator

def get_hardware_id():
    """Generate unique hardware ID based on system information"""
    try:
        # Get system information
        system_info = platform.system() + platform.version() + platform.machine()
        
        # Get additional hardware info on Windows
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic csproduct get uuid', shell=True).decode()
                uuid_result = result.split('\n')[1].strip()
                system_info += uuid_result
            except:
                pass
        
        # Add MAC address
        system_info += str(uuid.getnode())
        
        # Create hash and format
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback to simple MAC address
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

def create_signature(email, password, hwid):
    """Create SHA256 signature for verification"""
    data = f"{email}{password}{hwid}{SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()

def verify_license():
    """Verify if valid license exists"""
    if not os.path.exists(LICENSE_FILE):
        return False, "License file not found"
    
    try:
        # Read and decode license file
        with open(LICENSE_FILE, "r") as f:
            encoded_data = f.read().strip()
        
        decoded_data = base64.b64decode(encoded_data).decode()
        license_data = json.loads(decoded_data)
        
        # Extract license information
        stored_email = license_data.get("email", "")
        stored_hwid = license_data.get("hwid", "")
        stored_signature = license_data.get("signature", "")
        
        # Verify hardware ID
        current_hwid = get_hardware_id()
        if stored_hwid != current_hwid:
            return False, "Hardware ID mismatch"
        
        # Verify signature format
        if len(stored_signature) != 64:  # SHA256 length
            return False, "Invalid signature format"
        
        return True, f"Licensed to: {stored_email}"
        
    except Exception as e:
        return False, f"License verification failed: {str(e)}"

class SoftwareApp:
    def __init__(self, root):
        self.root = root
        self.root.title("My Software")
        self.root.geometry("500x450")
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.center_window()
        
        # Check license status
        is_valid, message = verify_license()
        
        if is_valid:
            self.show_main_application(message)
        else:
            self.show_activation_screen(message)

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = 500
        height = 450
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def show_activation_screen(self, error_message):
        """Show activation screen when license is invalid"""
        self.clear_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🔐 تسجيل الدخول",
                              font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#1976d2')
        title_label.pack(pady=(0, 20))
        
        # Error message
        error_label = tk.Label(main_frame, text=f"Status: {error_message}", 
                              font=("Arial", 10), bg='#f0f0f0', fg='#666')
        error_label.pack(pady=(0, 30))
        
        # Activation form
        form_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=25, pady=25)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        tk.Label(form_frame, text="Email Address:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.email_entry = tk.Entry(form_frame, font=("Arial", 10), width=40, 
                                   relief=tk.SOLID, bd=1)
        self.email_entry.pack(pady=(0, 15), ipady=5)
        
        # Password field
        tk.Label(form_frame, text="Password:", font=("Arial", 11, "bold"), 
                bg='#ffffff', fg='#333').pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(form_frame, show="*", font=("Arial", 10), 
                                      width=40, relief=tk.SOLID, bd=1)
        self.password_entry.pack(pady=(0, 20), ipady=5)
        
        # Login button
        login_btn = tk.Button(form_frame, text="🚀 تسجيل الدخول",
                               command=self.attempt_login,
                               font=("Arial", 11, "bold"), bg='#1976d2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=30, pady=8)
        login_btn.pack(pady=(0, 10))
        
        # Hardware ID section
        hw_frame = tk.Frame(main_frame, bg='#e3f2fd', relief=tk.SOLID, bd=1, padx=20, pady=15)
        hw_frame.pack(fill=tk.X)

        tk.Label(hw_frame, text="� معرف الجهاز:", font=("Arial", 11, "bold"),
                bg='#e3f2fd', fg='#1565c0').pack()

        hwid = get_hardware_id()
        self.hwid_var = tk.StringVar(value=hwid)

        hwid_entry = tk.Entry(hw_frame, textvariable=self.hwid_var, state="readonly",
                             font=("Courier", 10), bg='#ffffff', fg='#333',
                             relief=tk.SOLID, bd=1, justify=tk.CENTER, width=25)
        hwid_entry.pack(pady=(10, 15))

        # Copy all data button
        copy_all_btn = tk.Button(hw_frame, text="📋 نسخ بيانات التفعيل",
                               command=self.copy_activation_data,
                               font=("Arial", 11, "bold"), bg='#4caf50', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=25, pady=8)
        copy_all_btn.pack(pady=(0, 20))

        # Contact message
        tk.Label(hw_frame, text="للتفعيل تواصل معنا على:",
                font=("Arial", 12, "bold"), bg='#e3f2fd', fg='#1565c0').pack(pady=(0, 15))

        # Contact buttons frame
        contact_buttons_frame = tk.Frame(hw_frame, bg='#e3f2fd')
        contact_buttons_frame.pack(pady=(0, 15))

        # WhatsApp button
        whatsapp_btn = tk.Button(contact_buttons_frame, text="📱 واتساب",
                               command=lambda: self.open_link("https://wa.me/201200578402"),
                               font=("Arial", 10, "bold"), bg='#25D366', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        whatsapp_btn.pack(side=tk.LEFT, padx=8)

        # Facebook button
        facebook_btn = tk.Button(contact_buttons_frame, text="📘 فيسبوك",
                               command=lambda: self.open_link("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                               font=("Arial", 10, "bold"), bg='#1877f2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        facebook_btn.pack(side=tk.LEFT, padx=8)

        # Telegram button
        telegram_btn = tk.Button(contact_buttons_frame, text="✈️ تلجرام",
                               command=lambda: self.open_link("http://t.me/Mohamed_Abdo26"),
                               font=("Arial", 10, "bold"), bg='#0088cc', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        telegram_btn.pack(side=tk.LEFT, padx=8)

        # Instructions
        instructions = """📋 تعليمات التفعيل:
1. أدخل البريد الإلكتروني وكلمة المرور أعلاه
2. اضغط "نسخ بيانات التفعيل" لنسخ جميع البيانات
3. اضغط على أحد أزرار التواصل أعلاه
4. أرسل البيانات المنسوخة للحصول على ملف التفعيل"""

        tk.Label(hw_frame, text=instructions, font=("Arial", 9),
                bg='#e3f2fd', fg='#424242', justify=tk.LEFT).pack(pady=(10, 0))

    def show_main_application(self, license_info):
        """Show main application when license is valid"""
        self.clear_window()
        self.root.title("My Software - Licensed")
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=40)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Success header
        header_frame = tk.Frame(main_frame, bg='#e8f5e8', relief=tk.SOLID, bd=1, padx=20, pady=20)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        tk.Label(header_frame, text="✅ Software Successfully Activated!", 
                font=("Arial", 16, "bold"), bg='#e8f5e8', fg='#2e7d32').pack()
        
        tk.Label(header_frame, text=license_info, 
                font=("Arial", 10), bg='#e8f5e8', fg='#388e3c').pack(pady=(5, 0))
        
        # Application content
        content_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=30, pady=30)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(content_frame, text="🚀 Welcome to Your Licensed Software!", 
                font=("Arial", 18, "bold"), bg='#ffffff', fg='#1976d2').pack(pady=(0, 20))
        
        tk.Label(content_frame, text="All features are now available for use.\nThank you for your purchase!", 
                font=("Arial", 12), bg='#ffffff', fg='#666', justify=tk.CENTER).pack(pady=(0, 30))
        
        # Feature buttons
        features_frame = tk.Frame(content_frame, bg='#ffffff')
        features_frame.pack(pady=20)
        
        tk.Button(features_frame, text="📊 Analytics", command=self.feature_analytics,
                 font=("Arial", 10), bg='#ff9800', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="⚙️ Settings", command=self.feature_settings,
                 font=("Arial", 10), bg='#9c27b0', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(features_frame, text="📈 Reports", command=self.feature_reports,
                 font=("Arial", 10), bg='#00bcd4', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=12, pady=8).pack(side=tk.LEFT, padx=5)
        
        # License details
        details_frame = tk.Frame(content_frame, bg='#f5f5f5', relief=tk.SOLID, bd=1, padx=15, pady=15)
        details_frame.pack(fill=tk.X, pady=(30, 0))
        
        try:
            with open(LICENSE_FILE, "r") as f:
                encoded_data = f.read().strip()
            decoded_data = base64.b64decode(encoded_data).decode()
            license_data = json.loads(decoded_data)
            
            details_text = f"License Details:\nEmail: {license_data.get('email', 'Unknown')}\nHardware ID: {license_data.get('hwid', 'Unknown')}"
            tk.Label(details_frame, text=details_text, font=("Arial", 9), 
                    bg='#f5f5f5', fg='#666', justify=tk.LEFT).pack()
        except:
            pass
        
        # Exit button
        tk.Button(content_frame, text="❌ Exit Application", command=self.root.quit,
                 font=("Arial", 10), bg='#f44336', fg='white', relief=tk.FLAT,
                 cursor="hand2", width=20, pady=8).pack(pady=(20, 0))

    def attempt_login(self):
        """Attempt to login - if fails, show activation details"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return

        if "@" not in email:
            messagebox.showerror("خطأ", "يرجى إدخال بريد إلكتروني صحيح")
            return

        # Try to verify with existing license (this will always fail for new users)
        hwid = get_hardware_id()

        # Since this is for new users, show activation details
        activation_message = f"""طلب تفعيل البرنامج

البريد الإلكتروني: {email}
كلمة المرور: {password}
معرف الجهاز: {hwid}

يرجى إرسال هذه المعلومات للحصول على ملف التفعيل."""

        # Show activation dialog with contact links
        self.show_activation_dialog("بيانات التفعيل", activation_message, email, password, hwid)

    def open_link(self, url):
        """Open URL in default browser"""
        import webbrowser
        webbrowser.open(url)

    def copy_activation_data(self):
        """Copy all activation data to clipboard"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()
        hwid = get_hardware_id()

        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور أولاً")
            return

        activation_data = f"""طلب تفعيل البرنامج

البريد الإلكتروني: {email}
كلمة المرور: {password}
معرف الجهاز: {hwid}

يرجى إرسال هذه المعلومات للحصول على ملف التفعيل."""

        self.root.clipboard_clear()
        self.root.clipboard_append(activation_data)
        messagebox.showinfo("تم النسخ", "تم نسخ بيانات التفعيل!\nيمكنك الآن إرسالها عبر روابط التواصل.")

    def copy_hwid(self):
        """Copy hardware ID to clipboard"""
        hwid = self.hwid_var.get()
        self.root.clipboard_clear()
        self.root.clipboard_append(hwid)
        messagebox.showinfo("Copied", f"Hardware ID copied to clipboard:\n{hwid}")

    def show_activation_dialog(self, title, message, email, password, hwid):
        """Show activation dialog with contact links"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("600x500")
        dialog.resizable(False, False)
        dialog.configure(bg='#f0f0f0')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (300)
        y = (dialog.winfo_screenheight() // 2) - (250)
        dialog.geometry(f'600x500+{x}+{y}')

        # Main frame
        main_frame = tk.Frame(dialog, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        tk.Label(main_frame, text="📋 بيانات التفعيل",
                font=("Arial", 14, "bold"), bg='#f0f0f0', fg='#1976d2').pack(pady=(0, 20))

        # Message frame
        message_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1, padx=20, pady=20)
        message_frame.pack(fill=tk.X, pady=(0, 20))

        # Display activation data
        tk.Label(message_frame, text=message, font=("Courier", 10),
                bg='#ffffff', fg='#333', justify=tk.LEFT).pack()

        # Copy button
        def copy_all_data():
            dialog.clipboard_clear()
            dialog.clipboard_append(message)
            messagebox.showinfo("تم النسخ", "تم نسخ بيانات التفعيل!")

        tk.Button(message_frame, text="📋 نسخ بيانات التفعيل", command=copy_all_data,
                 font=("Arial", 11, "bold"), bg='#4caf50', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=25, pady=8).pack(pady=(15, 0))

        # Contact section
        contact_frame = tk.Frame(main_frame, bg='#e3f2fd', relief=tk.SOLID, bd=1, padx=20, pady=20)
        contact_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(contact_frame, text="للتفعيل تواصل معنا على:",
                font=("Arial", 12, "bold"), bg='#e3f2fd', fg='#1565c0').pack(pady=(0, 15))

        # Contact buttons frame
        contact_buttons_frame = tk.Frame(contact_frame, bg='#e3f2fd')
        contact_buttons_frame.pack()

        # WhatsApp button
        whatsapp_btn = tk.Button(contact_buttons_frame, text="📱 واتساب",
                               command=lambda: self.open_link("https://wa.me/201200578402"),
                               font=("Arial", 10, "bold"), bg='#25D366', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        whatsapp_btn.pack(side=tk.LEFT, padx=8)

        # Facebook button
        facebook_btn = tk.Button(contact_buttons_frame, text="📘 فيسبوك",
                               command=lambda: self.open_link("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
                               font=("Arial", 10, "bold"), bg='#1877f2', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        facebook_btn.pack(side=tk.LEFT, padx=8)

        # Telegram button
        telegram_btn = tk.Button(contact_buttons_frame, text="✈️ تلجرام",
                               command=lambda: self.open_link("http://t.me/Mohamed_Abdo26"),
                               font=("Arial", 10, "bold"), bg='#0088cc', fg='white',
                               relief=tk.FLAT, cursor="hand2", width=12, pady=8)
        telegram_btn.pack(side=tk.LEFT, padx=8)

        # Instructions
        instructions = """📋 تعليمات التفعيل:
1. اضغط "نسخ بيانات التفعيل" أعلاه
2. اضغط على أحد أزرار التواصل
3. أرسل البيانات المنسوخة للحصول على ملف التفعيل
4. ضع ملف التفعيل بجانب البرنامج وأعد التشغيل"""

        tk.Label(contact_frame, text=instructions, font=("Arial", 9),
                bg='#e3f2fd', fg='#424242', justify=tk.LEFT).pack(pady=(15, 0))

        # Close button
        tk.Button(main_frame, text="❌ إغلاق", command=dialog.destroy,
                 font=("Arial", 10, "bold"), bg='#f44336', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack()

    def show_copyable_dialog(self, title, message, hwid):
        """Show dialog with copyable text"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.configure(bg='#f0f0f0')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (250)
        y = (dialog.winfo_screenheight() // 2) - (200)
        dialog.geometry(f'500x400+{x}+{y}')

        # Main frame
        main_frame = tk.Frame(dialog, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        tk.Label(main_frame, text="📋 نسخ بيانات التفعيل",
                font=("Arial", 14, "bold"), bg='#f0f0f0', fg='#1976d2').pack(pady=(0, 20))

        # Instructions
        tk.Label(main_frame, text="انسخ النص أدناه وأرسله للحصول على ملف التفعيل:",
                font=("Arial", 10), bg='#f0f0f0', fg='#666').pack(pady=(0, 15))

        # Text area with message
        text_frame = tk.Frame(main_frame, bg='#ffffff', relief=tk.SOLID, bd=1)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        text_widget = tk.Text(text_frame, font=("Courier", 10), bg='#ffffff', fg='#333',
                             relief=tk.FLAT, bd=10, wrap=tk.WORD, selectbackground='#3498db')
        text_widget.pack(fill=tk.BOTH, expand=True)

        # Insert message and select all
        text_widget.insert(tk.END, message)
        text_widget.select_range(0, tk.END)
        text_widget.focus_set()

        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(fill=tk.X)

        # Copy all button
        def copy_all():
            self.root.clipboard_clear()
            self.root.clipboard_append(message)
            messagebox.showinfo("تم النسخ", "تم نسخ بيانات التفعيل!")

        tk.Button(buttons_frame, text="📋 نسخ الكل", command=copy_all,
                 font=("Arial", 10, "bold"), bg='#4caf50', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=15, pady=8).pack(side=tk.LEFT, padx=(0, 10))

        # Copy Hardware ID only
        def copy_hwid_only():
            self.root.clipboard_clear()
            self.root.clipboard_append(hwid)
            messagebox.showinfo("تم النسخ", f"تم نسخ معرف الجهاز:\n{hwid}")

        tk.Button(buttons_frame, text="💻 نسخ معرف الجهاز", command=copy_hwid_only,
                 font=("Arial", 10, "bold"), bg='#ff9800', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=18, pady=8).pack(side=tk.LEFT, padx=(0, 10))

        # Close button
        tk.Button(buttons_frame, text="❌ إغلاق", command=dialog.destroy,
                 font=("Arial", 10, "bold"), bg='#f44336', fg='white',
                 relief=tk.FLAT, cursor="hand2", width=10, pady=8).pack(side=tk.RIGHT)

        # Instructions at bottom
        instructions = """💡 التعليمات:
1. اضغط "نسخ الكل" لنسخ جميع البيانات
2. أو حدد نص معين واضغط Ctrl+C
3. أرسل النص المنسوخ عبر روابط التواصل للحصول على ملف التفعيل
4. ضع ملف التفعيل بجانب البرنامج وأعد التشغيل"""

        tk.Label(main_frame, text=instructions, font=("Arial", 8),
                bg='#f0f0f0', fg='#666', justify=tk.LEFT).pack(pady=(15, 0))

    def feature_analytics(self):
        """Sample analytics feature"""
        messagebox.showinfo("Analytics", "📊 Analytics module loaded!\nView your data insights here.")

    def feature_settings(self):
        """Sample settings feature"""
        messagebox.showinfo("Settings", "⚙️ Settings panel opened!\nConfigure your preferences here.")

    def feature_reports(self):
        """Sample reports feature"""
        messagebox.showinfo("Reports", "📈 Reports module loaded!\nGenerate and export reports here.")

    def clear_window(self):
        """Clear all widgets from window"""
        for widget in self.root.winfo_children():
            widget.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = SoftwareApp(root)
    root.mainloop()
